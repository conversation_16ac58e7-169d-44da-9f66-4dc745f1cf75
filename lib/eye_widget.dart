import 'package:flutter/material.dart';
import 'eye_shape.dart';

/// Custom painter for drawing eye shapes
class EyePainter extends CustomPainter {
  final EyePoints eyePoints;
  final Color eyeColor;
  final double strokeWidth;
  final bool filled;

  EyePainter({
    required this.eyePoints,
    this.eyeColor = Colors.black,
    this.strokeWidth = 2.0,
    this.filled = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = eyeColor
      ..strokeWidth = strokeWidth
      ..style = filled ? PaintingStyle.fill : PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    // Calculate center and scale
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double scale = size.width / 100; // Adjust scale based on widget size

    // Create path for upper eyelid
    final Path upperPath = Path();
    if (eyePoints.upper.isNotEmpty) {
      final Point2D firstUpper = eyePoints.upper.first;
      upperPath.moveTo(
        centerX + firstUpper.x * scale,
        centerY + firstUpper.y * scale,
      );

      for (int i = 1; i < eyePoints.upper.length; i++) {
        final Point2D point = eyePoints.upper[i];
        upperPath.lineTo(
          centerX + point.x * scale,
          centerY + point.y * scale,
        );
      }
    }

    // Create path for lower eyelid
    final Path lowerPath = Path();
    if (eyePoints.lower.isNotEmpty) {
      final Point2D firstLower = eyePoints.lower.first;
      lowerPath.moveTo(
        centerX + firstLower.x * scale,
        centerY + firstLower.y * scale,
      );

      for (int i = 1; i < eyePoints.lower.length; i++) {
        final Point2D point = eyePoints.lower[i];
        lowerPath.lineTo(
          centerX + point.x * scale,
          centerY + point.y * scale,
        );
      }
    }

    // Draw the eye shape
    if (filled) {
      // Create a combined path for filled eye
      final Path combinedPath = Path();
      combinedPath.addPath(upperPath, Offset.zero);
      combinedPath.addPath(lowerPath, Offset.zero);
      combinedPath.close();
      canvas.drawPath(combinedPath, paint);
    } else {
      // Draw upper and lower eyelids separately
      canvas.drawPath(upperPath, paint);
      canvas.drawPath(lowerPath, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}

/// Widget for displaying a single eye
class EyeWidget extends StatelessWidget {
  final EyePoints eyePoints;
  final double size;
  final Color eyeColor;
  final double strokeWidth;
  final bool filled;

  const EyeWidget({
    super.key,
    required this.eyePoints,
    this.size = 100,
    this.eyeColor = Colors.black,
    this.strokeWidth = 2.0,
    this.filled = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: EyePainter(
          eyePoints: eyePoints,
          eyeColor: eyeColor,
          strokeWidth: strokeWidth,
          filled: filled,
        ),
      ),
    );
  }
}

/// Widget for displaying both eyes
class BothEyesWidget extends StatelessWidget {
  final BothEyePoints bothEyePoints;
  final double eyeSize;
  final double spacing;
  final Color eyeColor;
  final double strokeWidth;
  final bool filled;

  const BothEyesWidget({
    super.key,
    required this.bothEyePoints,
    this.eyeSize = 100,
    this.spacing = 20,
    this.eyeColor = Colors.black,
    this.strokeWidth = 2.0,
    this.filled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        EyeWidget(
          eyePoints: bothEyePoints.left,
          size: eyeSize,
          eyeColor: eyeColor,
          strokeWidth: strokeWidth,
          filled: filled,
        ),
        SizedBox(width: spacing),
        EyeWidget(
          eyePoints: bothEyePoints.right,
          size: eyeSize,
          eyeColor: eyeColor,
          strokeWidth: strokeWidth,
          filled: filled,
        ),
      ],
    );
  }
}

/// Widget that generates and displays random eye shapes
class RandomEyesWidget extends StatefulWidget {
  final double eyeSize;
  final double spacing;
  final Color eyeColor;
  final double strokeWidth;
  final bool filled;
  final double eyeWidth;

  const RandomEyesWidget({
    super.key,
    this.eyeSize = 100,
    this.spacing = 20,
    this.eyeColor = Colors.black,
    this.strokeWidth = 2.0,
    this.filled = false,
    this.eyeWidth = 50,
  });

  @override
  State<RandomEyesWidget> createState() => _RandomEyesWidgetState();
}

class _RandomEyesWidgetState extends State<RandomEyesWidget> {
  late BothEyePoints _bothEyePoints;

  @override
  void initState() {
    super.initState();
    _generateNewEyes();
  }

  void _generateNewEyes() {
    setState(() {
      _bothEyePoints = EyeShapeGenerator.generateBothEyes(width: widget.eyeWidth);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        BothEyesWidget(
          bothEyePoints: _bothEyePoints,
          eyeSize: widget.eyeSize,
          spacing: widget.spacing,
          eyeColor: widget.eyeColor,
          strokeWidth: widget.strokeWidth,
          filled: widget.filled,
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: _generateNewEyes,
          child: const Text('Generate New Eyes'),
        ),
      ],
    );
  }
}
