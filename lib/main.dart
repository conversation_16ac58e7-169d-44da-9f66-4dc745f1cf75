import 'package:flutter/material.dart';
import 'eye_widget.dart';

void main() {
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ugly Avatar - Eye Shape Generator',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const EyeShapeDemo(),
    );
  }
}

class EyeShapeDemo extends StatelessWidget {
  const EyeShapeDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ugly Avatar - Eye Shapes'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: const Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Random Eye Shape Generator',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 20),
              Text(
                'This is a Flutter port of the JavaScript ugly-avatar eye shape generator.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 40),
              RandomEyesWidget(
                eyeSize: 120,
                spacing: 30,
                eyeColor: Colors.black,
                strokeWidth: 2.5,
                filled: false,
                eyeWidth: 60,
              ),
              SizedBox(height: 40),
              Text(
                'Filled Version:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 20),
              RandomEyesWidget(
                eyeSize: 100,
                spacing: 25,
                eyeColor: Colors.blue,
                strokeWidth: 1.0,
                filled: true,
                eyeWidth: 50,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
